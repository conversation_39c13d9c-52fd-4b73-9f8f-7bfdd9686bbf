<template>
  <div class="drawer-preview-example">
    <h3>抽屉式文件预览示例</h3>
    
    <div class="file-list">
      <div 
        v-for="file in sampleFiles" 
        :key="file.id"
        class="file-item"
        @click="showDrawerPreview(file)"
      >
        <el-icon><Document /></el-icon>
        <span>{{ file.name }}</span>
        <el-button type="primary" size="small">预览</el-button>
      </div>
    </div>

    <!-- 抽屉式预览组件 -->
    <FileDrawerPreview
      v-model="showDrawer"
      :file="selectedFile"
      :preview-mode="'original'"
    />
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { Document } from '@element-plus/icons-vue'
import FileDrawerPreview from './FileDrawerPreview.vue'

const showDrawer = ref(false)
const selectedFile = ref(null)

// 示例文件数据
const sampleFiles = ref([
  {
    id: 1,
    name: 'sample.pdf',
    fileType: 'pdf',
    mimeType: 'application/pdf'
  },
  {
    id: 2,
    name: 'image.jpg',
    fileType: 'image',
    mimeType: 'image/jpeg'
  },
  {
    id: 3,
    name: 'document.docx',
    fileType: 'word',
    mimeType: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  }
])

const showDrawerPreview = (file) => {
  selectedFile.value = file
  showDrawer.value = true
}
</script>

<style scoped>
.drawer-preview-example {
  padding: 20px;
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  max-width: 400px;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border: 1px solid #dcdfe6;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.file-item:hover {
  border-color: #409eff;
  background-color: #f0f9ff;
}

.file-item span {
  flex: 1;
  font-size: 14px;
}
</style>
