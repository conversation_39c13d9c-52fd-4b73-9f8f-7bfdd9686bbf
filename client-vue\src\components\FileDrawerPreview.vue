<template>
  <el-drawer
    v-model="visible"
    :title="drawerTitle"
    direction="ltr"
    :size="drawerWidth"
    :before-close="handleClose"
    class="file-drawer-preview"
    destroy-on-close
  >
    <div class="drawer-container" v-loading="loading">
      <!-- 图片预览 -->
      <div v-if="previewType === 'image'" class="image-preview">
        <div class="image-viewer">
          <img
            ref="previewImage"
            :src="imageUrl"
            :alt="file?.name"
            class="preview-image"
            :style="imageStyle"
            @load="handleImageLoad"
            @error="handleImageError"
          />
        </div>
        
        <!-- 图片控制按钮 -->
        <div class="image-controls">
          <el-button-group>
            <el-button @click="zoomOut" size="small" :disabled="scale <= 0.1">
              <el-icon><ZoomOut /></el-icon>
            </el-button>
            <el-button @click="resetZoom" size="small" class="zoom-level-btn">
              {{ Math.round(scale * 100) }}%
            </el-button>
            <el-button @click="zoomIn" size="small" :disabled="scale >= 3">
              <el-icon><ZoomIn /></el-icon>
            </el-button>
          </el-button-group>
        </div>
      </div>

      <!-- PDF预览 -->
      <div v-else-if="previewType === 'pdf'" class="pdf-preview">
        <!-- PDF 内容区域 -->
        <div class="pdf-container" ref="pdfViewerContainer">
          <div v-if="loading" class="pdf-loading">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>加载中...</span>
          </div>
          <div v-else-if="error" class="pdf-error">
            <el-icon size="48" color="#f56c6c"><Warning /></el-icon>
            <p>{{ error }}</p>
            <el-button @click="retryPdfLoad">重试</el-button>
          </div>
          <div v-else class="pdf-content" :style="pdfContentStyle">
            <!-- 始终渲染 Canvas，用 v-show 控制显示 -->
            <canvas
              ref="pdfCanvas"
              class="pdf-page"
              v-show="pdfLoaded && pdfPageCount > 0"
            ></canvas>
            <div v-show="!pdfLoaded || pdfPageCount === 0">
              <p>等待 PDF 加载完成...</p>
            </div>
          </div>
        </div>

        <!-- PDF工具栏 - 移动到底部 -->
        <div class="pdf-toolbar">
          <div class="pdf-controls">
            <el-button-group>
              <el-button
                :disabled="currentPage <= 1"
                @click="previousPage"
                size="small"
              >
                上一页
              </el-button>
              <span class="page-info">
                第 {{ currentPage }} / {{ pdfPageCount }} 页
              </span>
              <el-button
                :disabled="currentPage >= pdfPageCount"
                @click="nextPage"
                size="small"
              >
                下一页
              </el-button>
            </el-button-group>
            <div class="zoom-controls">
              <el-button @click="zoomOutPdf" size="small" :disabled="pdfScale <= 0.5">
                <el-icon><ZoomOut /></el-icon>
              </el-button>
              <el-button @click="resetPdfZoom" size="small" class="zoom-level-btn">
                {{ Math.round(pdfScale * 100) }}%
              </el-button>
              <el-button @click="zoomInPdf" size="small" :disabled="pdfScale >= 3">
                <el-icon><ZoomIn /></el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- Word文档预览 -->
      <div v-else-if="previewType === 'word'" class="word-preview">
        <div class="word-notice">
          <el-icon size="48"><Document /></el-icon>
          <h3>Word文档</h3>
          <p>{{ file?.name }}</p>
          <p class="notice-text">Word文档暂不支持在线预览</p>
        </div>
      </div>

      <!-- 不支持预览的文件类型 -->
      <div v-else class="unsupported-preview">
        <div class="unsupported-notice">
          <el-icon size="48"><Document /></el-icon>
          <h3>文件预览</h3>
          <p>{{ file?.name }}</p>
          <p class="notice-text">此文件类型暂不支持预览</p>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-if="error" class="error-state">
        <el-icon size="48" color="#f56c6c"><Warning /></el-icon>
        <h3>预览失败</h3>
        <p>{{ error }}</p>
        <el-button @click="retryPreview">重试</el-button>
      </div>
    </div>
  </el-drawer>
</template>

<script setup>
import { ref, computed, watch, nextTick, getCurrentInstance, onUnmounted } from 'vue'
import { Document, Warning, ZoomIn, ZoomOut, Loading } from '@element-plus/icons-vue'
import { filesApi } from '../services/api'
import { loadPdfDocument, checkPdfJsCompatibility } from '../utils/pdfUtils'

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  file: {
    type: Object,
    default: null
  },
  previewMode: {
    type: String,
    default: 'original', // 'original' 或 'watermark'
    validator: (value) => ['original', 'watermark'].includes(value)
  }
})

const emit = defineEmits(['update:modelValue'])

// 响应式数据
const loading = ref(false)
const error = ref('')
const scale = ref(1)
const previewImage = ref(null)

// PDF相关数据
const pdfViewerContainer = ref(null)
// 重要：pdfDoc 不能使用响应式，否则会出现私有字段访问错误
let pdfDoc = null
const pdfLoaded = ref(false) // 响应式标志，用于模板条件判断
const currentPage = ref(1)
const pdfPageCount = ref(0)
const pdfScale = ref(1)
const renderTask = ref(null)

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const drawerTitle = computed(() => {
  const modeText = props.previewMode === 'watermark' ? '水印预览' : '原件预览'
  return props.file ? `${modeText} - ${props.file.name}` : '文件预览'
})

// 计算抽屉宽度，保持1.2:1.4宽高比
const drawerWidth = computed(() => {
  const viewportHeight = window.innerHeight
  const drawerHeight = viewportHeight * 0.9 // 90%视口高度
  const drawerWidth = (drawerHeight * 1.2) / 1.4 // 1.2:1.4宽高比
  return `${Math.min(drawerWidth, window.innerWidth * 0.6)}px` // 最大不超过60%视口宽度
})

const previewType = computed(() => {
  if (!props.file) return null

  const fileType = props.file.fileType?.toLowerCase()
  const mimeType = props.file.mimeType?.toLowerCase()

  if (fileType === 'image' || mimeType?.startsWith('image/')) {
    return 'image'
  } else if (fileType === 'pdf' || mimeType === 'application/pdf') {
    return 'pdf'
  } else if (fileType === 'word' || mimeType?.includes('word') || mimeType?.includes('document')) {
    return 'word'
  }

  return 'unsupported'
})

const imageUrl = computed(() => {
  if (!props.file || previewType.value !== 'image') return null

  // 根据预览模式返回不同的URL
  if (props.previewMode === 'watermark') {
    // 这里应该返回带水印的预览URL，暂时使用原图
    return filesApi.getPreviewUrl(props.file.id)
  } else {
    // 原件预览
    return filesApi.getPreviewUrl(props.file.id)
  }
})

const imageStyle = computed(() => {
  return {
    transform: `scale(${scale.value})`,
    transition: 'transform 0.3s ease'
  }
})

const pdfContentStyle = computed(() => {
  return {
    transform: `scale(${pdfScale.value})`,
    transformOrigin: 'top center',
    transition: 'transform 0.3s ease'
  }
})

// 图片相关方法
const handleImageLoad = () => {
  loading.value = false
}

const handleImageError = () => {
  loading.value = false
  error.value = '图片加载失败'
}

const zoomIn = () => {
  if (scale.value < 3) {
    scale.value = Math.min(3, scale.value + 0.2)
  }
}

const zoomOut = () => {
  if (scale.value > 0.1) {
    scale.value = Math.max(0.1, scale.value - 0.2)
  }
}

const resetZoom = () => {
  scale.value = 1
}

// PDF相关方法
const loadPdf = async () => {
  if (!props.file || previewType.value !== 'pdf') return

  try {
    loading.value = true
    error.value = ''
    pdfLoaded.value = false
    console.log('Starting PDF load for file:', props.file.id)

    // 检查PDF.js兼容性
    if (!checkPdfJsCompatibility()) {
      throw new Error('PDF.js库初始化失败，请刷新页面重试')
    }

    // 清除之前的文档
    if (pdfDoc) {
      pdfDoc.destroy()
    }

    const url = filesApi.getPreviewUrl(props.file.id)
    console.log('PDF URL:', url)

    // 取消之前的渲染任务
    if (renderTask.value) {
      try {
        renderTask.value.cancel()
      } catch (cancelError) {
        console.warn('Failed to cancel previous render task:', cancelError)
      }
      renderTask.value = null
    }

    // 使用工具函数加载PDF文档
    console.log('Loading PDF document...')
    pdfDoc = await loadPdfDocument(url)
    pdfPageCount.value = pdfDoc.numPages
    currentPage.value = 1

    console.log(`PDF loaded successfully: ${pdfPageCount.value} pages`)

    // 先设置加载完成和PDF已加载标志，让Canvas元素显示出来
    loading.value = false
    pdfLoaded.value = true

    // 等待下一个tick确保canvas已经渲染
    await nextTick()

    // 渲染第一页
    await renderPage(1)

  } catch (err) {
    console.error('PDF加载失败:', err)

    // 提供更友好的错误信息
    let errorMessage = 'PDF文档加载失败'
    if (err.message.includes('网络')) {
      errorMessage = 'PDF加载失败: 网络连接问题'
    } else if (err.message) {
      errorMessage = err.message
    }

    error.value = errorMessage
    loading.value = false
    pdfLoaded.value = false
  }
}

const renderPage = async (pageNum) => {
  if (!pdfDoc || pageNum < 1 || pageNum > pdfPageCount.value) {
    return
  }

  try {
    // 使用安全的页面获取方法
    const page = await pdfDoc.getPage(pageNum)

    // 获取 canvas 元素
    await nextTick()
    const canvasRefs = getCurrentInstance()?.refs
    let canvas = canvasRefs?.pdfCanvas

    if (!canvas) {
      // 备选方案：直接通过 DOM 查询
      const containerEl = pdfViewerContainer.value
      canvas = containerEl?.querySelector('canvas')

      if (!canvas) {
        throw new Error(`找不到 PDF Canvas 元素`)
      }
    }

    const context = canvas.getContext('2d')

    // 设置缩放
    const viewport = page.getViewport({ scale: pdfScale.value })

    // 支持HiDPI屏幕
    const outputScale = window.devicePixelRatio || 1

    // 设置canvas尺寸
    canvas.width = Math.floor(viewport.width * outputScale)
    canvas.height = Math.floor(viewport.height * outputScale)
    canvas.style.width = Math.floor(viewport.width) + "px"
    canvas.style.height = Math.floor(viewport.height) + "px"

    // 清除canvas
    context.clearRect(0, 0, canvas.width, canvas.height)

    // 创建transform矩阵
    const transform = outputScale !== 1
      ? [outputScale, 0, 0, outputScale, 0, 0]
      : null

    // 渲染页面
    const renderContext = {
      canvasContext: context,
      transform: transform,
      viewport: viewport
    }

    await page.render(renderContext).promise

    console.log(`页面 ${pageNum} 渲染完成`)
  } catch (err) {
    // 提供具体的错误信息
    error.value = `渲染页面 ${pageNum} 失败: ${err.message}`
    console.error(`页面渲染错误:`, err)
  }
}

const previousPage = async () => {
  if (currentPage.value > 1) {
    currentPage.value--
    await nextTick()
    await renderPage(currentPage.value)
  }
}

const nextPage = async () => {
  if (currentPage.value < pdfPageCount.value) {
    currentPage.value++
    await nextTick()
    await renderPage(currentPage.value)
  }
}

const zoomInPdf = async () => {
  if (pdfScale.value < 3) {
    pdfScale.value = Math.min(3, pdfScale.value + 0.2)
    await nextTick()
    await renderPage(currentPage.value)
  }
}

const zoomOutPdf = async () => {
  if (pdfScale.value > 0.5) {
    pdfScale.value = Math.max(0.5, pdfScale.value - 0.2)
    await nextTick()
    await renderPage(currentPage.value)
  }
}

const resetPdfZoom = async () => {
  pdfScale.value = 1
  await nextTick()
  await renderPage(currentPage.value)
}

const retryPdfLoad = () => {
  error.value = ''
  loadPdf()
}

const retryPreview = () => {
  error.value = ''
  loading.value = true

  nextTick(() => {
    if (previewImage.value) {
      previewImage.value.src = imageUrl.value
    }
  })
}

const handleClose = () => {
  visible.value = false
  error.value = ''
  scale.value = 1

  // 清理PDF资源
  if (renderTask.value) {
    renderTask.value.cancel()
    renderTask.value = null
  }
  if (pdfDoc) {
    pdfDoc.destroy()
    pdfDoc = null
  }
  pdfLoaded.value = false
  currentPage.value = 1
  pdfPageCount.value = 0
  pdfScale.value = 1
}

// 监听文件变化
watch(() => props.file, async (newFile) => {
  if (newFile && visible.value) {
    error.value = ''
    scale.value = 1
    pdfScale.value = 1

    // 根据文件类型处理
    if (previewType.value === 'image') {
      loading.value = true
    } else if (previewType.value === 'pdf') {
      await loadPdf()
    } else {
      loading.value = false
    }
  }
}, { immediate: true })

// 监听抽屉显示状态
watch(visible, async (newVisible) => {
  if (newVisible && props.file) {
    error.value = ''
    scale.value = 1
    pdfScale.value = 1

    // 根据文件类型处理
    if (previewType.value === 'image') {
      loading.value = true
    } else if (previewType.value === 'pdf') {
      await loadPdf()
    } else {
      loading.value = false
    }
  } else if (!newVisible) {
    // 抽屉关闭时重置状态
    error.value = ''
    scale.value = 1
    loading.value = false

    // 清理PDF资源
    if (renderTask.value) {
      renderTask.value.cancel()
      renderTask.value = null
    }
    if (pdfDoc) {
      pdfDoc.destroy()
      pdfDoc = null
    }
    pdfLoaded.value = false
    currentPage.value = 1
    pdfPageCount.value = 0
    pdfScale.value = 1
  }
})

// 监听预览类型变化
watch(previewType, async (newType) => {
  if (newType && visible.value) {
    // 根据文件类型处理
    if (newType === 'image') {
      loading.value = true
    } else if (newType === 'pdf') {
      await loadPdf()
    } else {
      loading.value = false
    }
  }
})

// 组件卸载时确保清理资源
onUnmounted(() => {
  if (pdfDoc) {
    pdfDoc.destroy()
  }
})
</script>

<style scoped>
.file-drawer-preview {
  --el-drawer-padding-primary: 0;
}

/* 移除抽屉头部间距 */
.file-drawer-preview :deep(.el-drawer__header) {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

.file-drawer-preview :deep(.el-drawer .el-drawer__header) {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

/* 更强的选择器 */
:deep(.el-drawer.file-drawer-preview .el-drawer__header) {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

.file-drawer-preview :deep(.el-drawer__body) {
  padding: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.drawer-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.image-preview,
.pdf-preview,
.word-preview,
.unsupported-preview {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.image-viewer {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  background: #fff;
  margin: 10px;
  border-radius: 8px;
  border: 1px solid #dcdfe6;
}

.preview-image {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  cursor: grab;
  user-select: none;
}

.preview-image:active {
  cursor: grabbing;
}

.image-controls {
  display: flex;
  justify-content: center;
  padding: 10px;
  background: #fff;
  border-top: 1px solid #dcdfe6;
}

.pdf-toolbar {
  background: #fff;
  padding: 10px;
  border-top: 1px solid #dcdfe6;
  margin-top: auto; /* 推到底部 */
}

.pdf-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.page-info {
  padding: 0 10px;
  font-size: 14px;
  color: #606266;
}

.zoom-controls {
  display: flex;
  align-items: center;
  gap: 5px;
}

.zoom-level-btn {
  min-width: 60px;
}

.pdf-container {
  flex: 1;
  overflow: auto;
  background: #fff;
  border-radius: 8px;
  border: 1px solid #dcdfe6;
}

.pdf-content {
  display: flex;
  justify-content: center;
  padding: 20px 0;
}

.pdf-page {
  border: 1px solid #ddd;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 100%;
  height: auto;
}

.pdf-loading,
.pdf-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
  color: #606266;
}

.pdf-loading .el-icon {
  font-size: 32px;
  margin-bottom: 10px;
}

.word-notice,
.unsupported-notice {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
  color: #606266;
  background: #fff;
  margin: 20px;
  border-radius: 8px;
  border: 1px solid #dcdfe6;
}

.word-notice h3,
.unsupported-notice h3 {
  margin: 16px 0 8px;
  color: #303133;
}

.word-notice p,
.unsupported-notice p {
  margin: 8px 0;
  color: #606266;
}

.notice-text {
  color: #909399 !important;
  font-size: 14px;
}

.error-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  text-align: center;
  color: #f56c6c;
  background: #fff;
  margin: 20px;
  border-radius: 8px;
  border: 1px solid #f56c6c;
}

.error-state h3 {
  margin: 16px 0 8px;
  color: #f56c6c;
}

.error-state p {
  margin: 8px 0;
  color: #606266;
}
</style>

<style>
/* 全局样式 - 移除抽屉头部间距 */
.file-drawer-preview .el-drawer__header {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}
</style>
